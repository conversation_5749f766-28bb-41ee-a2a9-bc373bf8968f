import * as z from "zod/v4";
import type {
	FunctionDeclaration,
	JSONSchema,
} from "../../models/function-declaration";
import type { ToolContext } from "../tool-context";
import { BaseTool } from "./base-tool";

/**
 * Configuration for creating a tool
 */
export interface CreateToolConfig<
	T extends Record<string, any> = Record<string, never>,
> {
	/** The name of the tool */
	name: string;
	/** A description of what the tool does */
	description: string;
	/** Zod schema for validating tool arguments (optional, defaults to empty object) */
	schema?: z.ZodSchema<T>;
	/** The function to execute (can be sync or async) */
	fn: (args: T, context?: ToolContext) => any;
	/** Whether the tool is a long running operation */
	isLongRunning?: boolean;
	/** Whether the tool execution should be retried on failure */
	shouldRetryOnFailure?: boolean;
	/** Maximum retry attempts */
	maxRetryAttempts?: number;
}

/**
 * A tool implementation created by createTool
 */
class CreatedTool<T extends Record<string, any>> extends BaseTool {
	private func: (args: T, context?: ToolContext) => any;
	private schema: z.ZodSchema<T>;
	private functionDeclaration: FunctionDeclaration;

	constructor(config: CreateToolConfig<T>) {
		super({
			name: config.name,
			description: config.description,
			isLongRunning: config.isLongRunning ?? false,
			shouldRetryOnFailure: config.shouldRetryOnFailure ?? false,
			maxRetryAttempts: config.maxRetryAttempts ?? 3,
		});

		this.func = config.fn;
		// If no schema is provided, default to empty object schema
		this.schema = config.schema ?? (z.object({}) as unknown as z.ZodSchema<T>);
		this.functionDeclaration = this.buildDeclaration();
	}

	/**
	 * Executes the tool function with validation
	 */
	async runAsync(args: any, context: ToolContext): Promise<any> {
		try {
			// Validate arguments using Zod schema
			const validatedArgs = this.schema.parse(args);

			// Call the function with validated arguments.
			// `Promise.resolve` handles both sync and async functions gracefully.
			// Check if the function expects arguments by checking if it's an empty object schema
			const isEmptySchema =
				this.schema instanceof z.ZodObject &&
				Object.keys(this.schema.shape).length === 0;

			const result = await Promise.resolve(
				isEmptySchema && this.func.length <= 1
					? (this.func as any)(context)
					: this.func(validatedArgs, context),
			);

			// Ensure we return an object, but preserve falsy values like 0, false, ""
			return result ?? {};
		} catch (error) {
			if (error instanceof z.ZodError) {
				return {
					error: `Invalid arguments for ${this.name}: ${z.prettifyError(error)}`,
				};
			}
			return {
				error: `Error executing ${this.name}: ${error instanceof Error ? error.message : String(error)}`,
			};
		}
	}

	/**
	 * Returns the function declaration for this tool
	 */
	getDeclaration(): FunctionDeclaration {
		return this.functionDeclaration;
	}

	/**
	 * Builds the function declaration from the Zod schema
	 */
	private buildDeclaration(): FunctionDeclaration {
		const parameters = z.toJSONSchema(this.schema) as JSONSchema;

		return {
			name: this.name,
			description: this.description,
			parameters,
		};
	}
}

/**
 * Creates a tool from a configuration object.
 *
 * This is a more user-friendly alternative to FunctionTool that provides:
 * - Automatic argument validation using Zod schemas
 * - Clear error messages for invalid inputs
 * - Automatic JSON Schema generation for LLM function declarations
 * - Support for both sync and async functions
 * - Optional ToolContext parameter support
 * - Optional schema parameter (defaults to empty object for tools with no parameters)
 *
 * @param config The tool configuration object
 * @returns A BaseTool instance ready for use with agents
 *
 * @example
 * ```typescript
 * import { createTool } from '@iqai/adk';
 * import { z } from 'zod';
 *
 * // Tool with parameters
 * const calculatorTool = createTool({
 *   name: 'calculator',
 *   description: 'Performs basic arithmetic operations',
 *   schema: z.object({
 *     operation: z.enum(['add', 'subtract', 'multiply', 'divide']),
 *     a: z.number().describe('First number'),
 *     b: z.number().describe('Second number')
 *   }),
 *   fn: ({ operation, a, b }) => {
 *     switch (operation) {
 *       case 'add': return { result: a + b };
 *       case 'subtract': return { result: a - b };
 *       case 'multiply': return { result: a * b };
 *       case 'divide': return { result: b !== 0 ? a / b : 'Cannot divide by zero' };
 *       default: return { error: 'Unknown operation' };
 *     }
 *   }
 * });
 *
 * // Tool without parameters (schema is optional)
 * const greetingTool = createTool({
 *   name: 'greeting',
 *   description: 'Returns a greeting message',
 *   fn: () => ({ message: 'Hello, World!' })
 * });
 * ```
 */

// Overload for tools without parameters
export function createTool(config: {
	name: string;
	description: string;
	fn: (context?: ToolContext) => any;
	isLongRunning?: boolean;
	shouldRetryOnFailure?: boolean;
	maxRetryAttempts?: number;
	schema?: never; // Explicitly exclude schema
}): BaseTool;

// Overload for tools with parameters
export function createTool<T extends Record<string, any>>(
	config: CreateToolConfig<T>,
): BaseTool;

// Implementation
export function createTool<
	T extends Record<string, any> = Record<string, never>,
>(
	config:
		| CreateToolConfig<T>
		| {
				name: string;
				description: string;
				fn: (context?: ToolContext) => any;
				isLongRunning?: boolean;
				shouldRetryOnFailure?: boolean;
				maxRetryAttempts?: number;
				schema?: never;
		  },
): BaseTool {
	// Check if this is a no-parameter tool
	if (!("schema" in config) || config.schema === undefined) {
		const noParamConfig = config as {
			name: string;
			description: string;
			fn: (context?: ToolContext) => any;
			isLongRunning?: boolean;
			shouldRetryOnFailure?: boolean;
			maxRetryAttempts?: number;
		};

		// Create a wrapped config that matches the expected interface
		const wrappedConfig: CreateToolConfig<Record<string, never>> = {
			...noParamConfig,
			schema: z.object({}),
			fn: (_args: Record<string, never>, context?: ToolContext) =>
				noParamConfig.fn(context),
		};

		return new CreatedTool(wrappedConfig as any);
	}

	return new CreatedTool(config as CreateToolConfig<T>);
}
